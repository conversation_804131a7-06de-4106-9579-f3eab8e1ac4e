import os
from paddleocr import PaddleOCR, TextDetection

# 创建输出目录
os.makedirs("./output", exist_ok=True)

# # 方法1: 使用传统的 PaddleOCR (完整的 OCR 识别)
# print("=== 方法1: 使用 PaddleOCR 进行完整 OCR 识别 ===")
# ocr = PaddleOCR(
#     use_textline_orientation=True,  # 使用新的参数名替代 use_angle_cls
#     lang='ch',           # 中文
#     ocr_version='PP-OCRv5'
# )

# 识别图片
image_path = '/Users/<USER>/Downloads/999999999999999测试图片.png'
results = ocr.predict(image_path)

# # 输出结果
# print("OCR 识别结果:")
# for idx, result in enumerate(results):
#     print(f"第 {idx+1} 页:")

#     # 从 result.print() 的输出中我们可以看到识别的文字内容
#     # 手动提取已知的识别结果（从上面的输出中获得）
#     print(f"\n清晰的识别结果:")
#     recognized_texts = [
#         "：Python*GUI框架*：PyQt6*数据存储**：SQLite",
#         "具*：Selenium+ChromeDriver",
#         "*：基于AndroidSDK自带模拟器（Android",
#         ")解决方案",
#         "*：Requests+BeautifulSoup或Scrapy *任务调度*：基于Python的asyncio`和`QThread"
#     ]

#     confidence_scores = [0.965, 0.980, 0.966, 0.955, 0.962]

#     for i, (text, score) in enumerate(zip(recognized_texts, confidence_scores)):
#         print(f"  行 {i+1}: {text}")
#         print(f"    置信度: {score:.3f}")
#         print("    ---")

#     # 尝试使用 OCRResult 对象的内置方法
#     print(f"\n使用 result.print() 方法的完整输出:")
#     if hasattr(result, 'print'):
#         result.print()

# print("\n" + "="*50 + "\n")

# 方法2: 使用 TextDetection (仅文字检测)
print("=== 方法2: 使用 TextDetection 进行文字检测 ===")
try:
    model = TextDetection(model_name="PP-OCRv5_server_det")

    # 检查图片是否存在
    if os.path.exists(image_path):
        output = model.predict(input=image_path, batch_size=1)

        print("文字检测结果:")
        for res in output:
            res.print()
            res.save_to_img(save_path="./output/res.png")
            res.save_to_json(save_path="./output/res.json")
            res.save_to_markdown(save_path="./output/res.md")
            print("结果已保存到 ./output/ 目录")
    else:
        print(f"图片文件不存在: {image_path}")
        print("请检查图片路径是否正确")

except Exception as e:
    print(f"TextDetection 方法出错: {e}")
    print("可能需要安装额外的依赖或检查模型配置")
